{"name": "gridjs-svelte", "type": "module", "version": "2.1.0", "description": "A Svelte wrapper component for Grid.js", "scripts": {"build": "svelte-kit package", "dev:web": "svelte-kit dev", "build:web": "svelte-kit build", "test": "vitest run", "test:watch": "vitest watch", "check": "svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint --ignore-path .gitignore .", "format": "prettier --ignore-path .gitignore --write --plugin-search-dir=. ."}, "keywords": ["svelte", "gridjs"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"gridjs": ">=5.0.0", "svelte": ">=3.30.0"}, "devDependencies": {"@sveltejs/adapter-auto": "next", "@sveltejs/kit": "next", "@sveltejs/vite-plugin-svelte": "1.0.0-next.34", "@testing-library/jest-dom": "5.16.1", "@testing-library/svelte": "3.0.3", "@typescript-eslint/eslint-plugin": "4.31", "@typescript-eslint/parser": "4.31", "eslint": "7.32", "eslint-config-prettier": "8.3", "eslint-plugin-svelte3": "3.2", "gridjs": "5.0", "jsdom": "19.0.0", "prettier": "2.4", "prettier-plugin-svelte": "2.4", "svelte": "3.44", "svelte-check": "2.2", "svelte-preprocess": "4.9", "svelte2tsx": "0.4.13", "tslib": "2.3", "typescript": "4.4", "vitest": "0.0.142"}, "repository": {"type": "git", "url": "https://github.com/iamyuu/gridjs-svelte"}, "bugs": {"url": "https://github.com/iamyuu/gridjs-svelte/issues"}, "homepage": "https://gridjs.io/docs/integrations/svelte"}
<script>
	import Grid from "$lib/gridjs.svelte";
	import salaryPlugin from "../components/table-salary-plugin.js";

	const columns = ["Name", "Sal<PERSON>"];
	const data = [
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON><PERSON><PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON><PERSON><PERSON>", Math.round(Math.random() * 100000)],
		["Crystel", Math.round(Math.random() * 100000)],
		["Katarina", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["Hanna", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["<PERSON>", Math.round(Math.random() * 100000)],
		["Britney", Math.round(Math.random() * 100000)],
	];

	let config = {
		sort: true,
		search: true,
		pagination: true,
	};
</script>

<fieldset>
	<legend>Update the config</legend>

	{#each Object.keys(config) as configKey}
		<label>
			<input type="checkbox" bind:checked={config[configKey]} />
			{config[configKey] ? "Disable" : "Enable"}
			{configKey}
		</label>
	{/each}
</fieldset>

<Grid {columns} {data} plugins={[salaryPlugin]} {...config} />

<style global>
	@import "https://cdn.jsdelivr.net/npm/gridjs/dist/theme/mermaid.min.css";

	fieldset {
		border-radius: 5px;
		border: 1px solid #d2d6dc;
	}

	fieldset > * + * {
		margin-right: 2.5rem;
	}

	legend {
		background-color: #f9fafb;
		color: #6b7280;
	}
</style>
